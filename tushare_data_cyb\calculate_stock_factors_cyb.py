# coding: utf-8
import pandas as pd
import numpy as np
import talib
import datetime
import os
import warnings
from joblib import Parallel, delayed
import multiprocessing
from tqdm.auto import tqdm
from scipy import stats
warnings.filterwarnings('ignore')
warnings.filterwarnings("ignore", category=RuntimeWarning)

# --- 1. 配置参数 ---
STOCK_BASIC_FILE = 'stock_basic_cyb.csv'
STOCK_DAILY_FILE = 'stock_daily_cyb.csv'
DAILY_BASIC_FILE = 'daily_basic_cyb.csv'
OUTPUT_FILE = 'stock_factors_cyb.csv'

# 计算参数
ATR_WINDOW = 14
MA_WINDOWS = [5, 10, 20]
BIAS_WINDOWS = [10, 20]
BB_WINDOW = 20
BB_STD = 2
MIN_OBS_FOR_CALC =  60
N_JOBS = multiprocessing.cpu_count() - 1

# --- 2. 数据加载与预处理函数 ---
def load_and_preprocess_data(stock_basic_file, stock_daily_file, daily_basic_file):
    """加载所有CSV文件并进行初步预处理 - 优化版本"""
    print("开始加载CSV文件...")

    try:
        # 使用更高效的读取方式
        print("加载股票基本信息...")
        stock_basic = pd.read_csv(stock_basic_file, encoding='utf-8')

        print("加载股票日线数据...")
        daily_cols = ['ts_code', 'trade_date', 'pre_close', 'open', 'high', 'low', 'close', 'pct_chg', 'vol', 'amount']
        stock_daily = pd.read_csv(stock_daily_file, encoding='utf-8', usecols=lambda x: x in daily_cols)

        print("加载每日基本面数据...")
        basic_cols = ['ts_code', 'trade_date', 'pe', 'pb', 'turnover_rate', 'volume_ratio', 'total_mv']
        daily_basic = pd.read_csv(daily_basic_file, encoding='utf-8', usecols=lambda x: x in basic_cols)

    except FileNotFoundError as e:
        print(f"错误：找不到文件 {e.filename}。请确保所有CSV文件都在指定路径下。")
        return None, None, None, None, None
    except Exception as e:
        print(f"加载文件时出错: {e}")
        return None, None, None, None, None

    print("开始数据清洗和类型转换...")

    # --- 数据清洗和类型转换 ---
    # 股票基础信息
    print("处理股票基础信息...")
    if 'list_date' in stock_basic.columns:
        stock_basic['list_date'] = pd.to_datetime(stock_basic['list_date'], format='%Y%m%d', errors='coerce')
    if 'delist_date' in stock_basic.columns:
        stock_basic['delist_date'] = pd.to_datetime(stock_basic['delist_date'], format='%Y%m%d', errors='coerce')
        # 只保留未摘牌的股票基本信息
        stock_basic = stock_basic[stock_basic['delist_date'].isna()]

    # 选择需要的列
    basic_keep_cols = [col for col in ['ts_code', 'name', 'area', 'industry', 'list_date'] if col in stock_basic.columns]
    stock_basic = stock_basic[basic_keep_cols]
    stock_basic = stock_basic.drop_duplicates(subset=['ts_code'])

    # 股票日行情
    print("处理股票日线数据...")
    stock_daily['trade_date'] = pd.to_datetime(stock_daily['trade_date'], format='%Y%m%d')

    # 批量转换数值列
    price_cols = ['pre_close', 'open', 'high', 'low', 'close', 'vol', 'amount', 'pct_chg']
    for col in price_cols:
        if col in stock_daily.columns:
            stock_daily[col] = pd.to_numeric(stock_daily[col], errors='coerce')

    # 过滤无效数据
    stock_daily = stock_daily.dropna(subset=['ts_code', 'trade_date', 'close'])
    stock_daily = stock_daily.sort_values(by=['ts_code', 'trade_date'])

    # 每日基本面数据
    print("处理每日基本面数据...")
    if not daily_basic.empty:
        daily_basic['trade_date'] = pd.to_datetime(daily_basic['trade_date'], format='%Y%m%d')
        # 转换数值列
        numeric_cols = ['pe', 'pb', 'turnover_rate', 'volume_ratio', 'total_mv']
        for col in numeric_cols:
            if col in daily_basic.columns:
                daily_basic[col] = pd.to_numeric(daily_basic[col], errors='coerce')
        daily_basic = daily_basic.sort_values(by=['ts_code', 'trade_date'])

    # --- 合并数据 ---
    print("开始合并数据...")

    # 首先合并股票日线和基本信息
    print("合并股票日线和基本信息...")
    stock_merged = pd.merge(stock_daily, stock_basic, on='ts_code', how='left')

    # 合并每日基本面数据
    print("合并每日基本面数据...")
    if not daily_basic.empty:
        stock_merged = pd.merge(stock_merged, daily_basic, on=['ts_code', 'trade_date'], how='left', suffixes=('', '_basic'))
    else:
        # 如果没有基本面数据，添加空列
        for col in ['pe', 'pb', 'turnover_rate']:
            stock_merged[col] = np.nan

    # 不再合并财务数据

    print("最终排序...")
    stock_merged = stock_merged.sort_values(by=['ts_code', 'trade_date'])

    print(f"数据合并完成！最终数据量: {len(stock_merged)} 行")
    return stock_basic, stock_daily, daily_basic, stock_merged


# --- 3. 目标函数 ---
def calculate_target_return(A, B):
    AA = A.shift(-1)
    BB = B.shift(-2)
    target = (BB / AA -1)*100
    return target

# --- 4. 传统技术因子 ---
def calculate_momentum(close, window):
    """计算动量因子"""
    return (close / close.shift(window) - 1) * 100

def calculate_ma(close, window):
    """计算移动平均线"""
    return close.rolling(window=window).mean()

def calculate_ma_ratio(close, window):
    """计算价格相对移动平均线的比率"""
    ma = calculate_ma(close, window)
    return (close / ma - 1) * 100

def calculate_volatility(close, window):
    """计算波动率"""
    returns = close.pct_change()
    return returns.rolling(window=window).std() * np.sqrt(252) * 100

def calculate_rsi(close, window=14):
    """计算RSI指标"""
    try:
        return talib.RSI(close.values, timeperiod=window)
    except:
        return pd.Series(np.nan, index=close.index)

def calculate_macd(close):
    """计算MACD指标"""
    try:
        macd, macdsignal, macdhist = talib.MACD(close.values)
        return pd.Series(macd, index=close.index), pd.Series(macdsignal, index=close.index), pd.Series(macdhist, index=close.index)
    except:
        return pd.Series(np.nan, index=close.index), pd.Series(np.nan, index=close.index), pd.Series(np.nan, index=close.index)

def calculate_bollinger_bands(close, window=20, std_dev=2):
    """计算布林带指标"""
    try:
        upper, middle, lower = talib.BBANDS(close.values, timeperiod=window, nbdevup=std_dev, nbdevdn=std_dev)
        bb_ratio = (close - pd.Series(lower, index=close.index)) / (pd.Series(upper, index=close.index) - pd.Series(lower, index=close.index))
        return bb_ratio * 100
    except:
        return pd.Series(np.nan, index=close.index)

def calculate_atr(high, low, close, window=14):
    """计算ATR指标"""
    try:
        atr = talib.ATR(high.values, low.values, close.values, timeperiod=window)
        return pd.Series(atr, index=close.index)
    except:
        return pd.Series(np.nan, index=close.index)

# --- 5. 优化传统因子 ---
def calculate_adaptive_ma(close, window_short=10, window_long=30):
    """自适应移动平均"""
    ma_short = calculate_ma(close, window_short)
    ma_long = calculate_ma(close, window_long)
    # 当短期均线上穿长期均线时给予更高权重
    signal = (ma_short > ma_long).astype(int)
    adaptive_ma = signal * ma_short + (1 - signal) * ma_long
    return (close / adaptive_ma - 1) * 100

def calculate_volume_weighted_momentum(close, volume, window):
    """成交量加权动量"""
    returns = close.pct_change()
    volume_weight = volume / volume.rolling(window=window).mean()
    weighted_returns = returns * volume_weight
    return weighted_returns.rolling(window=window).sum() * 100

def calculate_volatility_adjusted_momentum(close, window):
    """波动率调整动量"""
    momentum = calculate_momentum(close, window)
    volatility = calculate_volatility(close, window)
    return momentum / (volatility + 1e-8)  # 避免除零

# --- 6. 创新因子 ---
def calculate_price_acceleration(close, window=10):
    """价格加速度因子"""
    returns = close.pct_change()
    acceleration = returns.diff()
    return acceleration.rolling(window=window).mean() * 100

def calculate_price_efficiency_ratio(close, window=20):
    """价格效率比率"""
    direction = abs(close - close.shift(window))
    volatility = close.diff().abs().rolling(window=window).sum()
    efficiency = direction / (volatility + 1e-8)
    return efficiency * 100

# --- 7. 复合因子 ---
def calculate_momentum_volatility_composite(close, window=20):
    """动量-波动率复合因子"""
    momentum = calculate_momentum(close, window)
    volatility = calculate_volatility(close, window)
    # 高动量低波动率的股票得分更高
    composite = momentum / (volatility + 1e-8)
    return composite

def calculate_trend_strength(close, volume, window=20):
    """趋势强度复合因子"""
    price_trend = calculate_momentum(close, window)
    volume_trend = (volume / volume.shift(window) - 1) * 100
    # 价格和成交量同向变化时趋势更强
    trend_strength = price_trend * np.sign(volume_trend)
    return trend_strength

# --- 8. 防追高因子 ---
def calculate_overextension_factor(close, high, window=20):
    """过度延伸因子"""
    ma = calculate_ma(close, window)
    distance_from_ma = (close - ma) / ma * 100
    recent_high = high.rolling(window=window).max()
    distance_from_high = (close - recent_high) / recent_high * 100
    # 距离均线和近期高点越远，追高风险越大
    overextension = distance_from_ma + distance_from_high
    return -overextension  # 负值表示风险，正值表示机会

def calculate_pullback_opportunity(close, window=10):
    """回调机会因子"""
    recent_high = close.rolling(window=window).max()
    pullback_ratio = (recent_high - close) / recent_high * 100
    # 适度回调后的机会
    opportunity = np.where((pullback_ratio > 2) & (pullback_ratio < 15), pullback_ratio, 0)
    return opportunity


# --- 9. 基本面因子 ---

def calculate_pe_relative(pe_series, window=60):
    """相对PE因子"""
    if pe_series.isna().all():
        return pd.Series(np.nan, index=pe_series.index)
    pe_ma = pe_series.rolling(window=window).mean()
    pe_relative = (pe_series - pe_ma) / pe_ma * 100
    return pe_relative

def calculate_pb_relative(pb_series, window=60):
    """相对PB因子"""
    if pb_series.isna().all():
        return pd.Series(np.nan, index=pb_series.index)
    pb_ma = pb_series.rolling(window=window).mean()
    pb_relative = (pb_series - pb_ma) / pb_ma * 100
    return pb_relative



def calculate_mfi(high, low, close, volume, window=14):
    """计算资金流指标 (MFI)"""
    try:
        return talib.MFI(high.values, low.values, close.values, volume.values, timeperiod=window)
    except:
        return pd.Series(np.nan, index=close.index)

def calculate_adosc(high, low, close, volume, fast_period=3, slow_period=10):
    """计算佳庆A/D振荡器"""
    try:
        return talib.ADOSC(high.values, low.values, close.values, volume.values, fastperiod=fast_period, slowperiod=slow_period)
    except:
        return pd.Series(np.nan, index=close.index)

def calculate_obv_ratio(close, volume, window=10):
    """计算OBV与其移动平均线的比率"""
    try:
        obv = talib.OBV(close.values, volume.values)
        obv_series = pd.Series(obv, index=close.index)
        obv_ma = obv_series.rolling(window=window).mean()
        # 避免除以0，并处理obv_ma可能为0的情况
        obv_ratio = (obv_series - obv_ma) / (obv_ma.replace(0, 1e-6))
        return obv_ratio * 100
    except:
        return pd.Series(np.nan, index=close.index)

def calculate_shadow_lines(open_price, high, low, close):
    """计算标准化后的上影线和下影线长度"""
    price_range = high - low
    price_range = price_range.replace(0, 1e-6) # 避免除以0

    upper_shadow = (high - pd.concat([open_price, close], axis=1).max(axis=1)) / price_range
    lower_shadow = (pd.concat([open_price, close], axis=1).min(axis=1) - low) / price_range
    return upper_shadow * 100, lower_shadow * 100

def calculate_real_body_ratio(open_price, high, low, close):
    """计算K线实体占总振幅的比例"""
    price_range = high - low
    price_range = price_range.replace(0, 1e-6)

    real_body = abs(open_price - close) / price_range
    return real_body * 100

def calculate_close_position(high, low, close):
    """计算收盘价在当天振幅中的位置"""
    price_range = high - low
    price_range = price_range.replace(0, 1e-6)

    position = (close - low) / price_range
    return position * 100

def calculate_turnover_rate_anomaly(turnover_rate, window=20):
    """计算换手率异常度（与移动平均值的比）"""
    if turnover_rate.isna().all():
        return pd.Series(np.nan, index=turnover_rate.index)

    turnover_ma = turnover_rate.rolling(window=window).mean()
    anomaly = turnover_rate / (turnover_ma.replace(0, 1e-6))
    return anomaly

def calculate_size_factor(market_value):
    """计算市值因子（市值的对数）"""
    return np.log(market_value + 1) # +1避免log(0)


def calculate_return_skew_kurt(close, window=20):
    """计算滚动收益率的偏度和峰度"""
    returns = close.pct_change()
    skewness = returns.rolling(window=window).skew()
    kurtosis = returns.rolling(window=window).kurt()
    return skewness, kurtosis

def calculate_downside_risk(close, window=20):
    """计算下行风险（使用负收益率的标准差）"""
    returns = close.pct_change()
    # 将正收益率设为0
    negative_returns = returns.copy()
    negative_returns[negative_returns > 0] = 0
    downside_risk = negative_returns.rolling(window=window).std()
    return downside_risk * np.sqrt(252) * 100 # 年化


# --- 12. 老因子 ---

def calculate_natr(high, low, close, window=14):
    """计算NATR"""
    natr = talib.NATR(high, low, close, timeperiod=window)
    return natr

def calculate_kdj(high, low, close, n=9, m1=3, m2=3):
    """KDJ指标"""
    low_list = low.rolling(window=n).min()
    high_list = high.rolling(window=n).max()

    rsv = (close - low_list) / (high_list - low_list + 1e-10) * 100
    k = rsv.ewm(com=m1-1, adjust=False).mean()
    d = k.ewm(com=m2-1, adjust=False).mean()
    j = 3 * k - 2 * d

    return k, d, j

def calculate_volatility_regime(close, short_window=10, long_window=30):
    """波动率体制"""
    returns = close.pct_change()
    short_vol = returns.rolling(window=short_window).std()
    long_vol = returns.rolling(window=long_window).std()
    return short_vol / (long_vol + 1e-10)

def calculate_risk_oscillator(close, volume, high, low, window=14):
    """风险振荡器"""
    # 价格风险
    price_risk = calculate_volatility_regime(close, 5, 20)

    # 成交量风险
    volume_risk = volume.rolling(window=5).std() / volume.rolling(window=20).std()

    # 区间风险
    range_risk = (high - low) / close
    range_risk_ma = range_risk.rolling(window=window).mean()

    # 综合风险振荡器
    risk_osc = (price_risk * 0.4 +
                volume_risk * 0.3 +
                range_risk / range_risk_ma * 0.3)

    return risk_osc
def calculate_obv_indicators(close, volume):
    """OBV指标组"""
    obv = talib.OBV(close, volume)
    obv_trend = obv.rolling(window=20).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0])
    return obv, obv_trend



def calculate_adx_indicators(high, low, close, window=14):
    """ADX指标组"""
    adx = talib.ADX(high, low, close, timeperiod=window)
    plus_di = talib.PLUS_DI(high, low, close, timeperiod=window)
    minus_di = talib.MINUS_DI(high, low, close, timeperiod=window)
    di_diff = plus_di - minus_di
    return adx, plus_di, minus_di, di_diff

def calculate_reaction_speed_factor(close, volume, window=10):
    """反应速度因子"""
    # 价格反应速度
    price_change = close.pct_change()
    price_acceleration = price_change - price_change.shift(1)

    # 成交量反应速度
    volume_change = volume.pct_change()
    volume_acceleration = volume_change - volume_change.shift(1)

    # 反应速度综合
    reaction_speed = np.abs(price_acceleration) * (1 + np.abs(volume_acceleration))

    return reaction_speed.rolling(window=window).mean()

def calculate_range_expansion(high, low, close, window=20):
    """范围扩张"""
    daily_range = (high - low) / close
    avg_range = daily_range.rolling(window=window).mean()
    return daily_range / (avg_range + 1e-10)

def calculate_volume_price_trend(close, volume):
    """量价趋势"""
    # VPT指标
    vpt = ((close - close.shift(1)) / close.shift(1) * volume).cumsum()

    # VPT动量
    vpt_momentum = (vpt - vpt.shift(20)) / (np.abs(vpt.shift(20)) + 1e-10)

    return vpt_momentum
def calculate_normalized_composite_momentum(close, volume, high, low, window=20):
    """标准化复合动量"""
    # 各种动量指标
    price_momentum = calculate_momentum(close, window)
    volume_momentum = calculate_momentum(volume, window)
    range_momentum = calculate_momentum(high - low, window)

    # 标准化
    def normalize(series):
        return (series - series.rolling(window=window*3).mean()) / \
               (series.rolling(window=window*3).std() + 1e-10)

    norm_price = normalize(price_momentum)
    norm_volume = normalize(volume_momentum)
    norm_range = normalize(range_momentum)

    # 复合动量
    composite = (norm_price * 0.5 + norm_volume * 0.3 + norm_range * 0.2)

    return composite
def calculate_smart_money(close, volume, high, low):
    """聪明钱指标"""
    # 收盘位置
    close_position = (close - low) / (high - low + 1e-10)

    # 成交量分布
    smart_volume = volume * (2 * close_position - 1)
    smart_money = smart_volume.rolling(window=20).sum() / volume.rolling(window=20).sum()

    return smart_money
def calculate_stealth_accumulation(close, volume, low, high, window=20):
    """隐形吸筹"""
    # 价格稳定性
    price_stability = 1 / (close.pct_change().rolling(window=window).std() + 0.01)

    # 成交量稳定增长
    volume_trend = volume.rolling(window=5).mean() / \
                   volume.rolling(window=window).mean()

    # 低位盘整
    price_level = (close - low.rolling(window=window*2).min()) / \
                  (high.rolling(window=window*2).max() - low.rolling(window=window*2).min() + 1e-10)

    # 隐形吸筹指标
    stealth = price_stability * volume_trend * (1 - price_level)

    return stealth
def calculate_price_velocity_factor(close, window=20):
    """价格速度因子"""
    # 一阶导数（速度）
    velocity = close.pct_change()

    # 平滑速度
    smooth_velocity = velocity.rolling(window=5).mean()

    # 速度的动量
    velocity_momentum = smooth_velocity - smooth_velocity.shift(window)

    return velocity_momentum
def calculate_pressure_release(close, volume, high, window=20):
    """压力释放"""
    # 上方套牢盘压力
    trapped_level = high.rolling(window=window).max()
    pressure = (trapped_level - close) / close

    # 成交量释放
    volume_release = volume / volume.rolling(window=window).mean()

    # 压力释放指标
    pressure_release = pressure * (1 / (volume_release + 0.5))
    return pressure_release
def calculate_exhaustion_indicator(close, volume, high, window=20):
    """衰竭指标"""
    # 价格动能衰竭
    momentum_decay = calculate_momentum(close, 5) / calculate_momentum(close, 20)

    # 成交量衰竭
    volume_decay = volume.rolling(window=5).mean() / volume.rolling(window=20).mean()

    # 高点衰竭
    high_decay = (high.rolling(window=5).max() - high) / high

    # 综合衰竭指标
    exhaustion = momentum_decay * volume_decay * (1 + high_decay)

    return exhaustion

def calculate_rsi_divergence(close, rsi, window=20):
    """RSI背离"""
    price_trend = (close - close.shift(window)) / close.shift(window)
    rsi_trend = (rsi - rsi.shift(window)) / 50
    return price_trend - rsi_trend

def calculate_intraday_volatility_distribution(high, low, close, open_price):
    """日内波动在不同时段的分布"""
    morning_range = (high - open_price) / open_price
    afternoon_range = (close - low) / low
    distribution = morning_range / (afternoon_range + 1e-8)
    return distribution

def calculate_price_jump_detection(open_price, pre_close, volume, window=20):
    """隔夜跳空与日内跳跃的检测"""
    overnight_gap = (open_price - pre_close) / pre_close
    jump_intensity = abs(overnight_gap) * np.log(volume + 1)
    jump_frequency = (abs(overnight_gap) > 0.02).rolling(window=window).sum()
    return jump_intensity * jump_frequency
def calculate_volume_concentration(volume, amount, high, low, window=10):
    """成交量在价格区间内的集中度"""
    avg_price = amount / (volume + 1e-8)
    price_range = high - low
    concentration = volume / (price_range * avg_price + 1e-8)
    return concentration.rolling(window=window).mean()
def calculate_order_imbalance(close, high, low, volume):
    """基于价格位置推断的买卖压力"""
    buy_pressure = (close - low) / (high - low + 1e-8)
    sell_pressure = (high - close) / (high - low + 1e-8)
    imbalance = (buy_pressure - sell_pressure) * volume
    return imbalance.rolling(window=20).sum()
def calculate_micro_momentum(close, volume, window=5):
    """短期价格变化的加速度"""
    price_change = close.pct_change()
    volume_change = volume.pct_change()
    micro_momentum = price_change * (1 + volume_change) * 100
    return micro_momentum.rolling(window=window).mean()
def calculate_information_ratio(close, window=20):
    """超额收益的信息比率"""
    returns = close.pct_change()
    excess_returns = returns - returns.rolling(window=window*3).mean()
    tracking_error = excess_returns.rolling(window=window).std()
    ir = excess_returns.rolling(window=window).mean() / (tracking_error + 1e-8)
    return ir * np.sqrt(252)
def calculate_price_discovery_efficiency(open_price, close, high, low):
    """价格发现的效率"""
    daily_range = high - low
    open_to_close = abs(close - open_price)
    efficiency = open_to_close / (daily_range + 1e-8)
    return efficiency.rolling(window=20).mean() * 100
def calculate_liquidity_consumption(volume, high, low, close, window=20):
    """单位成交量对价格的影响"""
    price_impact = (high - low) / close
    volume_normalized = volume / volume.rolling(window=window).mean()
    liquidity_consumption = price_impact / (volume_normalized + 1e-8)
    return liquidity_consumption * 100
def calculate_quote_stability(high, low, close, volume, window=10):
    """报价的稳定程度"""
    spread = (high - low) / close
    spread_volatility = spread.rolling(window=window).std()
    volume_stability = volume.rolling(window=window).std() / volume.rolling(window=window).mean()
    quote_stability = 1 / (spread_volatility * volume_stability + 0.01)
    return quote_stability
def calculate_market_depth_proxy(volume, high, low, close):
    """市场深度的代理指标"""
    price_range_pct = (high - low) / close
    depth_proxy = np.log(volume + 1) / (price_range_pct + 0.001)
    return depth_proxy.rolling(window=20).mean()
def calculate_order_flow_toxicity(close, volume, window=20):
    """VPIN的简化版本"""
    returns = close.pct_change()
    volume_buckets = volume.rolling(window=window).sum() / window
    directional_volume = volume * np.sign(returns)
    toxicity = abs(directional_volume.rolling(window=window).sum()) / (volume_buckets * window + 1e-8)
    return toxicity

def calculate_fear_index_proxy(high, low, close, volume, window=20):
    """市场恐慌程度的代理指标"""
    daily_range = (high - low) / close
    volume_spike = volume / volume.rolling(window=window).mean()
    down_days = (close < close.shift(1)).rolling(window=window).sum()
    fear_index = daily_range * volume_spike * (down_days / window)
    return fear_index
def calculate_greed_index_proxy(close, volume, high, window=20):
    """市场贪婪程度的代理指标"""
    momentum = calculate_momentum(close, window)
    volume_ratio = volume / volume.rolling(window=window*2).mean()
    high_distance = (high - high.rolling(window=window).mean()) / high.rolling(window=window).mean()
    greed_index = momentum * volume_ratio * high_distance / 100
    return greed_index
def calculate_sentiment_transition(close, volume, window=20):
    """市场情绪的转换速度"""
    returns = close.pct_change()
    volume_change = volume.pct_change()

    # 情绪指标
    sentiment = returns * volume_change

    # 情绪变化率
    sentiment_change = sentiment - sentiment.shift(window)
    transition_speed = abs(sentiment_change).rolling(window=window).mean()

    return transition_speed
def calculate_extreme_sentiment(close, volume, high, low, window=20):
    """极端市场情绪的识别"""
    price_extreme = ((close - low.rolling(window=window).min()) /
                    (high.rolling(window=window).max() - low.rolling(window=window).min() + 1e-8))
    volume_extreme = volume / volume.rolling(window=window).quantile(0.9)
    extreme_sentiment = price_extreme * volume_extreme
    return extreme_sentiment
def calculate_sentiment_momentum(close, volume, turnover_rate, window=20):
    """市场情绪的动量"""
    # 综合情绪得分
    price_score = close / close.rolling(window=window).mean()
    volume_score = volume / volume.rolling(window=window).mean()
    turnover_score = turnover_rate / turnover_rate.rolling(window=window).mean()

    sentiment_score = (price_score + volume_score + turnover_score) / 3
    sentiment_momentum = sentiment_score - sentiment_score.shift(window)

    return sentiment_momentum
def calculate_dynamic_support_resistance(high, low, close, window=20):
    """动态计算的支撑阻力比"""
    # 动态阻力
    resistance = high.rolling(window=window).apply(lambda x: x.quantile(0.75))

    # 动态支撑
    support = low.rolling(window=window).apply(lambda x: x.quantile(0.25))

    # 当前位置
    position = (close - support) / (resistance - support + 1e-8)

    return position
def calculate_strength_persistence(close, window=20):
    """相对强度的持续性"""
    rsi = talib.RSI(close.values, timeperiod=14)
    rsi_series = pd.Series(rsi, index=close.index)

    # RSI在强势区域的持续时间
    strong_zone = (rsi_series > 60).astype(int)
    weak_zone = (rsi_series < 40).astype(int)

    strong_persistence = strong_zone.rolling(window=window).sum()
    weak_persistence = weak_zone.rolling(window=window).sum()

    persistence_factor = strong_persistence - weak_persistence

    return persistence_factor
def calculate_rsi_acceleration(close, window=14):
    """RSI的加速度"""
    rsi = talib.RSI(close.values, timeperiod=window)
    rsi_series = pd.Series(rsi, index=close.index)

    rsi_change = rsi_series.diff()
    rsi_acceleration = rsi_change.diff()

    return rsi_acceleration.rolling(window=5).mean()



# --- 11. 主函数：计算所有因子 ---
def calculate_factors_for_stock(stock_data, current_date=None):
    """为单个ts_code的股票数据计算所有因子"""
    ts_code = stock_data['ts_code'].iloc[0]

    # 确保数据按日期排序
    stock_data = stock_data.sort_values(by='trade_date')

    # 检查数据量是否足够进行计算
    if len(stock_data) < MIN_OBS_FOR_CALC:
        print(f"数据不足，跳过 {ts_code} (需要 {MIN_OBS_FOR_CALC} 天, 只有 {len(stock_data)} 天)")
        return None

    # 创建结果字典
    results_dict = {}

    # --- 1. 基础数据 ---
    results_dict['ts_code'] = stock_data['ts_code']
    results_dict['trade_date'] = stock_data['trade_date']
    results_dict['open'] = stock_data['open']
    results_dict['high'] = stock_data['high']
    results_dict['low'] = stock_data['low']
    results_dict['close'] = stock_data['close']
    results_dict['pct_chg'] = stock_data['pct_chg']
    results_dict['vol'] = stock_data['vol']
    results_dict['amount'] = stock_data['amount']

    # 提取常用价格序列
    close = stock_data['close'].astype(float)
    high = stock_data['high'].astype(float)
    low = stock_data['low'].astype(float)
    open_price = stock_data['open'].astype(float)
    volume = stock_data['vol'].astype(float)
    amount = stock_data['amount'].astype(float)
    pct_chg = stock_data['pct_chg'].astype(float)
    pre_close = stock_data['pre_close'].astype(float)

    # 基本面数据
    pe = stock_data.get('pe', pd.Series(np.nan, index=stock_data.index)).astype(float)
    pb = stock_data.get('pb', pd.Series(np.nan, index=stock_data.index)).astype(float)
    turnover_rate = stock_data.get('turnover_rate', pd.Series(np.nan, index=stock_data.index)).astype(float)
    volume_ratio = stock_data.get('volume_ratio', pd.Series(np.nan, index=stock_data.index)).astype(float)
    total_mv = stock_data.get('total_mv', pd.Series(np.nan, index=stock_data.index)).astype(float)

    # 目标函数（不是因子）
    results_dict['target_return_close'] = calculate_target_return(open_price, close)
    results_dict['target_return_high'] = calculate_target_return(open_price, high)

    # --- 2. 传统技术因子 ---
    for window in MA_WINDOWS:
        results_dict[f'momentum_{window}d'] = calculate_momentum(close, window)
        results_dict[f'ma_ratio_{window}d'] = calculate_ma_ratio(close, window)
        results_dict[f'volatility_{window}d'] = calculate_volatility(close, window)


    macd, macd_signal, macd_hist = calculate_macd(close)
    results_dict['macd'] = macd
    results_dict['macd_signal'] = macd_signal
    results_dict['macd_hist'] = macd_hist

    results_dict['bb_ratio'] = calculate_bollinger_bands(close, BB_WINDOW, BB_STD)
    results_dict['atr'] = calculate_atr(high, low, close, ATR_WINDOW)

    # --- 3. 优化传统因子 ---
    results_dict['adaptive_ma'] = calculate_adaptive_ma(close)
    results_dict['volume_weighted_momentum_10d'] = calculate_volume_weighted_momentum(close, volume, 10)
    results_dict['volume_weighted_momentum_20d'] = calculate_volume_weighted_momentum(close, volume, 20)
    results_dict['volatility_adjusted_momentum_10d'] = calculate_volatility_adjusted_momentum(close, 10)
    results_dict['volatility_adjusted_momentum_20d'] = calculate_volatility_adjusted_momentum(close, 20)

    # --- 4. 创新因子 ---
    results_dict['price_acceleration'] = calculate_price_acceleration(close)
    results_dict['price_efficiency_ratio'] = calculate_price_efficiency_ratio(close)

    # --- 5. 复合因子 ---
    results_dict['momentum_volatility_composite_10d'] = calculate_momentum_volatility_composite(close, 10)
    results_dict['momentum_volatility_composite_20d'] = calculate_momentum_volatility_composite(close, 20)
    results_dict['trend_strength_10d'] = calculate_trend_strength(close, volume, 10)
    results_dict['trend_strength_20d'] = calculate_trend_strength(close, volume, 20)

    # --- 6. 防追高因子 ---
    results_dict['overextension_factor_10d'] = calculate_overextension_factor(close, high, 10)
    results_dict['overextension_factor_20d'] = calculate_overextension_factor(close, high, 20)
    results_dict['pullback_opportunity'] = calculate_pullback_opportunity(close)


    # --- 7. 基本面因子（仅PE/PB） ---
    results_dict['pe_relative'] = calculate_pe_relative(pe)
    results_dict['pb_relative'] = calculate_pb_relative(pb)

    # --- 8.价量关系深度挖掘 ---
    results_dict['mfi_14d'] = pd.Series(calculate_mfi(high, low, close, volume, 14), index=close.index)
    results_dict['adosc'] = pd.Series(calculate_adosc(high, low, close, volume), index=close.index)
    results_dict['obv_ratio_10d'] = calculate_obv_ratio(close, volume, 10)

    # --- 9. K线形态与日内行为因子 ---
    upper_shadow, lower_shadow = calculate_shadow_lines(open_price, high, low, close)
    results_dict['upper_shadow'] = upper_shadow
    results_dict['lower_shadow'] = lower_shadow
    results_dict['real_body_ratio'] = calculate_real_body_ratio(open_price, high, low, close)
    results_dict['close_position'] = calculate_close_position(high, low, close)

    # --- 10. 拓展基本面与估值因子 ---
    results_dict['turnover_rate_anomaly_20d'] = calculate_turnover_rate_anomaly(turnover_rate, 20)
    results_dict['size_factor'] = calculate_size_factor(total_mv)
    results_dict['volume_ratio'] = volume_ratio

    # --- 11. 统计特征因子 ---
    skew_20d, kurt_20d = calculate_return_skew_kurt(close, 20)
    results_dict['skew_20d'] = skew_20d
    results_dict['kurt_20d'] = kurt_20d
    results_dict['downside_risk_20d'] = calculate_downside_risk(close, 20)

    # --- 12. 老因子 ---
    results_dict['natr'] = calculate_natr(high, low, close, 14)

    k, d, j = calculate_kdj(high, low, close)
    results_dict['kdj_k'] = k
    results_dict['kdj_d'] = d
    results_dict['kdj_j'] = j

    results_dict['volatility_regime'] = calculate_volatility_regime(close)

    obv, obv_trend = calculate_obv_indicators(close, volume)
    results_dict['obv'] = obv
    results_dict['obv_trend'] = obv_trend

    adx, plus_di, minus_di, di_diff = calculate_adx_indicators(high, low, close)
    results_dict['adx'] = adx
    results_dict['plus_di'] = plus_di
    results_dict['minus_di'] = minus_di
    results_dict['di_diff'] = di_diff

    results_dict['rsi'] = pd.Series(calculate_rsi(close, 14), index=close.index)
    results_dict['rsi_divergence'] = calculate_rsi_divergence(close, results_dict['rsi'])

    results_dict['reaction_speed_factor'] = calculate_reaction_speed_factor(close, volume)

    results_dict['range_expansion'] = calculate_range_expansion(high, low, close)

    results_dict['volume_price_trend'] = calculate_volume_price_trend(close, volume)

    results_dict['normalized_composite_momentum'] = calculate_normalized_composite_momentum(close, volume, high, low)
    results_dict['smart_money'] = calculate_smart_money(close, volume, high, low)

    results_dict['stealth_accumulation'] = calculate_stealth_accumulation(close, volume, low ,high)
    results_dict['exhaustion_indicator'] = calculate_exhaustion_indicator(close, volume, high)
    results_dict['price_velocity_factor'] = calculate_price_velocity_factor(close)
    results_dict['pressure_release'] = calculate_pressure_release(close, volume, high)
    results_dict['intraday_volatility_distribution'] = calculate_intraday_volatility_distribution(high, low, close, open_price)
    results_dict['price_jump_detection'] = calculate_price_jump_detection(open_price, pre_close, volume)
    results_dict['volume_concentration'] = calculate_volume_concentration(volume, amount, high, low)
    results_dict['order_imbalance'] = calculate_order_imbalance(close, high, low, volume)
    results_dict['micro_momentum'] = calculate_micro_momentum(close, volume)
    results_dict['information_ratio'] = calculate_information_ratio(close)
    results_dict['price_discovery_efficiency'] = calculate_price_discovery_efficiency(open_price, close, high, low)
    results_dict['liquidity_consumption'] = calculate_liquidity_consumption(volume, high, low, close)
    results_dict['quote_stability'] = calculate_quote_stability(high, low, close, volume)
    results_dict['market_depth_proxy'] = calculate_market_depth_proxy(volume, high, low, close)
    results_dict['order_flow_toxicity'] = calculate_order_flow_toxicity(close, volume)

    results_dict['fear_index_proxy'] = calculate_fear_index_proxy(high, low, close, volume)
    results_dict['greed_index_proxy'] = calculate_greed_index_proxy(close, volume, high)
    results_dict['sentiment_transition'] = calculate_sentiment_transition(close, volume)
    results_dict['extreme_sentiment'] = calculate_extreme_sentiment(close, volume, high, low)
    results_dict['sentiment_momentum'] = calculate_sentiment_momentum(close, volume, turnover_rate)
    results_dict['dynamic_support_resistance'] = calculate_dynamic_support_resistance(high, low, close)
    results_dict['strength_persistence'] = calculate_strength_persistence(close)
    results_dict['rsi_acceleration'] = calculate_rsi_acceleration(close)


    # --- 创建最终的DataFrame ---
    results_df = pd.DataFrame(results_dict, index=stock_data.index)
    results_df = results_df.replace([np.inf, -np.inf, np.nan], 0)


    # 使用MIN_OBS_FOR_CALC作为起始点，仅返回有足够历史数据的行
    if len(results_df) > MIN_OBS_FOR_CALC:
        return results_df.iloc[MIN_OBS_FOR_CALC-1:]
    else:
        return None  # 返回None而不是np.nan，保持一致性

# --- 12. 主程序 ---
if __name__ == '__main__':
    print("开始执行创业板股票因子计算...")
    start_time = datetime.datetime.now()

    # --- 加载和预处理数据 ---
    print("加载并预处理数据...")
    stock_basic, stock_daily, daily_basic, stock_merged = load_and_preprocess_data(
        STOCK_BASIC_FILE, STOCK_DAILY_FILE, DAILY_BASIC_FILE
    )

    if stock_merged is None:
        print("数据加载或预处理失败，程序终止。")
        exit()

    print(f"原始数据加载完成。股票行情数据 {len(stock_daily)} 条，合并后数据 {len(stock_merged)} 条。")

    # --- 增量更新处理 ---
    existing_data = None
    last_dates = {}
    if os.path.exists(OUTPUT_FILE):
        print(f"检测到已存在的结果文件: {OUTPUT_FILE}，将进行增量更新。")
        try:
            existing_data = pd.read_csv(OUTPUT_FILE, encoding='utf-8', parse_dates=['trade_date'])
            if not existing_data.empty:
                last_dates = existing_data.groupby('ts_code')['trade_date'].max().to_dict()
                print(f"已加载 {len(existing_data)} 条历史计算结果。")
            else:
                print("历史结果文件为空。")
        except Exception as e:
            print(f"加载历史结果文件失败: {e}。将重新计算所有数据。")
            existing_data = None
            last_dates = {}
    else:
        print("未找到历史结果文件，将计算所有数据。")

    # 过滤需要计算的数据 - 修复增量更新逻辑
    if last_dates:
        unique_ts_codes_in_merged = stock_merged['ts_code'].unique()
        codes_to_process = []
        data_to_process_list = []

        # 计算最大滚动窗口，确保有足够的历史数据
        max_window = max(MA_WINDOWS + BIAS_WINDOWS + [BB_WINDOW, ATR_WINDOW, MIN_OBS_FOR_CALC, 60])  # 60是基本面因子的窗口

        for code in unique_ts_codes_in_merged:
            last_calculated_date = last_dates.get(code)
            code_data = stock_merged[stock_merged['ts_code'] == code].sort_values('trade_date').reset_index(drop=True) # 重置索引很重要
            max_date_for_code = code_data['trade_date'].max()

            if last_calculated_date is None or max_date_for_code > last_calculated_date:
                # 关键修复：为增量更新提供足够的历史数据
                if last_calculated_date is not None:
                    # 计算需要的起始日期：从最后计算日期往前推足够的交易日
                    buffer_days = max_window + 10 # 缓冲区

                    # 找到last_calculated_date在数据中的位置
                    date_indices = code_data[code_data['trade_date'] <= last_calculated_date].index
                    if len(date_indices) > 0:
                        last_index = date_indices[-1]
                        # 往前推buffer_days个交易日作为起始点
                        start_index = max(0, last_index - buffer_days)
                        incremental_data = code_data.iloc[start_index:]
                    else:
                        # 如果找不到对应日期（例如新数据完全不连续），使用全部数据
                        incremental_data = code_data

                    # --- 关键修复：在这里检查incremental_data是否为空 ---
                    if incremental_data.empty:
                        print(f"股票 {code}: 增量更新筛选后无有效数据，跳过。")
                        continue # 跳过此股票

                    data_to_process_list.append(incremental_data)
                    # 这个打印现在是安全的
                    print(f"股票 {code}: 增量更新，从 {incremental_data['trade_date'].min().strftime('%Y-%m-%d')} 开始计算")
                    codes_to_process.append(code) # 只有在确认要处理时才添加

                else:
                    # 新股票，使用全部数据
                    incremental_data = code_data
                    if incremental_data.empty:
                        print(f"股票 {code}: 新股无数据，跳过。")
                        continue

                    data_to_process_list.append(incremental_data)
                    print(f"股票 {code}: 首次计算，使用全部数据")
                    codes_to_process.append(code)

        if not codes_to_process:
            print("没有新的数据需要计算。")
            exit()

        print(f"需要处理或更新 {len(codes_to_process)} 个股票的数据。")
        # 注意：这里的合并逻辑被移除了，因为并行计算时是按股票分组传入的
        # data_to_process = pd.concat(data_to_process_list, ignore_index=True) if data_to_process_list else pd.DataFrame()
        grouped_data = data_to_process_list # 直接使用列表

    else:
        data_to_process = stock_merged
        codes_to_process = data_to_process['ts_code'].unique()
        grouped_data = [group for _, group in data_to_process.groupby('ts_code')] # 全量计算时分组
        print("将计算所有股票的数据。")

    if not grouped_data:
         print("筛选后没有数据需要处理。")
         exit()

    # --- 并行计算 ---
    print(f"开始并行计算因子 (使用 {N_JOBS} 个进程)...")
    # grouped_data = [group for _, group in data_to_process.groupby('ts_code')] # 移动到上面的逻辑中

    results_list = Parallel(n_jobs=N_JOBS, verbose=10)(
        delayed(calculate_factors_for_stock)(stock_group) for stock_group in grouped_data
    )

    # --- 合并结果 ---
    print("合并计算结果...")
    valid_results = [res for res in results_list if res is not None and isinstance(res, pd.DataFrame) and not res.empty]

    if not valid_results:
        print("所有股票计算均失败或无有效结果。")
        new_results_df = pd.DataFrame()
    else:
        new_results_df = pd.concat(valid_results, ignore_index=True)
        new_results_df = new_results_df.sort_values(by=['ts_code', 'trade_date'])
        print(f"计算完成，得到 {len(new_results_df)} 条新因子数据。")

    # --- 合并新旧数据并保存 ---
    if existing_data is not None and not new_results_df.empty:
        print("合并新计算结果与历史结果...")
        
        # 为了正确替换更新的数据，我们应该移除所有被重新计算的股票的旧数据，然后追加全部新计算的结果
        codes_updated = new_results_df['ts_code'].unique()
        existing_data_clean = existing_data[~existing_data['ts_code'].isin(codes_updated)]
        print(f"从历史数据中移除 {len(codes_updated)} 个已更新股票的旧数据，保留 {len(existing_data_clean)} 条历史数据")

        # 合并数据
        final_df = pd.concat([existing_data_clean, new_results_df], ignore_index=True)
        final_df = final_df.sort_values(by=['ts_code', 'trade_date'])
        print(f"合并后数据量: {len(final_df)} 条")

    elif not new_results_df.empty:
        print("保存首次计算的结果...")
        final_df = new_results_df.sort_values(by=['ts_code', 'trade_date'])
    elif existing_data is not None:
         print("没有新数据需要更新，保留原始结果文件。")
         final_df = existing_data
    else:
         print("没有计算出任何结果，无法保存文件。")
         final_df = pd.DataFrame()

    #过滤最近一年的数据
    if not final_df.empty:
        # 获取数据中的最新日期
        latest_date = final_df['trade_date'].max()

        # 计算一年前的日期
        one_year_ago = latest_date - pd.DateOffset(years=1)

        # 过滤出最近一年的数据
        print(f"过滤数据：保留 {one_year_ago.strftime('%Y-%m-%d')} 至 {latest_date.strftime('%Y-%m-%d')} 的数据...")
        final_df_filtered = final_df[final_df['trade_date'] >= one_year_ago].copy()

        # 打印过滤前后的数据量对比
        print(f"过滤前数据量: {len(final_df)} 条")
        print(f"过滤后数据量: {len(final_df_filtered)} 条")
        print(f"删除了 {len(final_df) - len(final_df_filtered)} 条早于一年的历史数据")

        # 更新final_df为过滤后的数据
        final_df = final_df_filtered
    # 保存最终结果到CSV
    if not final_df.empty:
        try:
            final_df.to_csv(OUTPUT_FILE, index=False, encoding='utf-8-sig')
            print(f"结果已成功保存到: {OUTPUT_FILE}")
            print(f"最终文件包含 {len(final_df)} 条记录。")
        except Exception as e:
            print(f"保存结果文件失败: {e}")
    else:
        print("最终结果为空，未生成或更新文件。")

    end_time = datetime.datetime.now()
    print(f"总耗时: {end_time - start_time}")